package com.paic.ncbs.claim.common.util;

import java.math.BigDecimal;
import java.util.List;

public class BigDecimalUtils {

    public static final BigDecimal NEGATIVE_ONE = new BigDecimal(-1);

    public static BigDecimal max(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) > 0 ? a : b;
    }

    public static BigDecimal toPercent(BigDecimal a) {
        return a.divide(new BigDecimal("100"));
    }

    public static BigDecimal getScaleRoundHalfUp(BigDecimal dutyPayAmount, int num) {
        return dutyPayAmount.setScale(num, BigDecimal.ROUND_HALF_UP);
    }

    public static boolean isGreaterZero(BigDecimal number) {
        try {
            if (number == null) {
                return false;
            }
            if (number.compareTo(new BigDecimal(0)) <= 0) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }


    public static BigDecimal convertBigDecimal(BigDecimal num) {
        if (num == null) {
            num = BigDecimal.ZERO;
        }
        return num;
    }

    public static boolean compareBigDecimalMinus(BigDecimal bd1, BigDecimal bd2) {
        boolean returnFlag = false;
        int i1 = bd1.compareTo(bd2);
        if (i1 < 0) {
            returnFlag = true;
        }
        return returnFlag;
    }

    public static boolean compareBigDecimalPlus(BigDecimal bd1, BigDecimal bd2) {
        boolean returnFlag = false;
        int i1 = bd1.compareTo(bd2);
        if (i1 > 0) {
            returnFlag = true;
        }
        return returnFlag;
    }
    
     
    public static boolean compareBigDecimalPlusOrEqual(BigDecimal bd1, BigDecimal bd2) {
        boolean returnFlag = false;
        int i1 = bd1.compareTo(bd2);
        if (i1 > 0 || i1 == 0) {
            returnFlag = true;
        }
        return returnFlag;
    }

    public static boolean isNullBigDecimal(BigDecimal bigDecimal) {
        boolean result = false;

        if (bigDecimal == null) {
            result = true;
        }

        return result;
    }

     
    public static boolean isGreaterOrEqualToZero(BigDecimal bigDecimal) {
        boolean result = false;
        if (BigDecimalUtils.isNullBigDecimal(bigDecimal)) {
            return result;
        }

        if (bigDecimal.compareTo(BigDecimal.ZERO) >= 0) {
            result = true;
        }

        return result;

    }

     
    public static BigDecimal getBigDecimal(String bigDecimalStr) {
        BigDecimal result = BigDecimal.ZERO;

        if (!StringUtils.isEmptyStr(bigDecimalStr)) {
            result = new BigDecimal(bigDecimalStr);
        }

        return result;
    }

     
    public static BigDecimal getBigDecimal(Object bigDecimalStr) {
        if (bigDecimalStr instanceof String) {
            String str = (String) bigDecimalStr;
            BigDecimalUtils.getBigDecimal(str);
        }

        return BigDecimal.ZERO;
    }

    public static BigDecimal convertToBigDecimal(Object bigDecimalStr) {
        if (bigDecimalStr == null) {
            return null;
        }
        return new BigDecimal(bigDecimalStr.toString());
    }

    public static boolean isEqual(BigDecimal one, BigDecimal two) {
        if (one == null && two == null) {
            return true;
        }
        if ((one == null && two != null) || (one != null && two == null)) {
            return false;
        }
        return one.compareTo(two) == 0 ? true : false;
    }

    public static BigDecimal getPositive(BigDecimal dd) {
        if (dd.compareTo(BigDecimal.ZERO)>=0) {
            return dd;
        } else {
            return BigDecimal.ZERO;
        }
    }

    public static BigDecimal sum(List<BigDecimal> bds) {
        if (bds == null || bds.size() == 0) {
            return new BigDecimal("0.00");
        }
        BigDecimal sum = new BigDecimal("0.00");
        for (int i = 0; i < bds.size(); i++) {
            sum = sum.add(nvl(bds.get(i), 0));
        }
        return sum;
    }

    public static BigDecimal sum(BigDecimal... bds) {
        if (bds == null) {
            return new BigDecimal("0.00");
        }
        BigDecimal sum = new BigDecimal("0.00");
        for (BigDecimal bigDecimal : bds) {
            sum = sum.add(nvl(bigDecimal, 0));
        }
        return sum;
    }


    public static BigDecimal nvl(BigDecimal bd, int val) {
        if (bd == null) {
            return new BigDecimal(val);
        }
        return bd;
    }

    public static BigDecimal nvl(BigDecimal bd, String val) {
        if (bd == null) {
            return new BigDecimal(val);
        }
        return bd;
    }

    public static boolean isNullOrZero(BigDecimal number) {
        if (number == null) {
            return true;
        }
        if (number.compareTo(new BigDecimal(0)) == 0) {
            return true;
        }
        return false;
    }

    public static boolean compareBigDecimalAndNullMinus(BigDecimal bd1, BigDecimal bd2) {
        if (bd1 == null) {
            bd1 = new BigDecimal(0);
        }
        if (bd2 == null) {
            bd2 = new BigDecimal(0);
        }
        boolean returnFlag = false;
        int i1 = bd1.compareTo(bd2);
        if (i1 < 0) {
            returnFlag = true;
        }
        return returnFlag;
    }

    public static BigDecimal percentage(BigDecimal b1, BigDecimal b2) {
        if(null== b1){
            return BigDecimal.ZERO;
        }
        return b1.multiply(b2).divide(new BigDecimal(100));
    }

    public static String toString(BigDecimal bd) {
        if (bd == null) {
            return null;
        }
        bd = bd.setScale(2,BigDecimal.ROUND_HALF_UP);
        return bd.toPlainString();
    }

}
