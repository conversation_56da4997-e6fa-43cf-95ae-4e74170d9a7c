package com.paic.ncbs.claim.dao.autosettle;

import java.util.Date;

/**
 * 条款理赔配置表
 */
public class ClmsClauseConfig {

    /**
     * 主键
     */
    private String create;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 险种代码
     */
    private String planCode;

    /**
     * 险种名称
     */
    private String planName;

    /**
     * 产品工厂的版本
     */
    private String pfVersion;

    /**
     * 方案代码
     */
    private String riskGroupCode;

    /**
     * 方案名称
     */
    private String riskGroupName;

    /**
     * 版本号
     */
    private Integer versionNo;

    /**
     * 来源
     */
    private String defineSource;

    /**
     * 责任明细
     */
    private String dutyDetail;

    /**
     * 个团标记
     */
    private String igFlag;

    /**
     * 生效状态
     */
    private String validFlag;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 新增原因分类
     */
    private String addReason;

    /**
     * 新增原因描述
     */
    private String addReasonDesc;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPfVersion() {
        return pfVersion;
    }

    public void setPfVersion(String pfVersion) {
        this.pfVersion = pfVersion;
    }

    public String getRiskGroupCode() {
        return riskGroupCode;
    }

    public void setRiskGroupCode(String riskGroupCode) {
        this.riskGroupCode = riskGroupCode;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getDefineSource() {
        return defineSource;
    }

    public void setDefineSource(String defineSource) {
        this.defineSource = defineSource;
    }

    public String getDutyDetail() {
        return dutyDetail;
    }

    public void setDutyDetail(String dutyDetail) {
        this.dutyDetail = dutyDetail;
    }

    public String getIgFlag() {
        return igFlag;
    }

    public void setIgFlag(String igFlag) {
        this.igFlag = igFlag;
    }

    public String getValidFlag() {
        return validFlag;
    }

    public void setValidFlag(String validFlag) {
        this.validFlag = validFlag;
    }

    public Date getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getAddReason() {
        return addReason;
    }

    public void setAddReason(String addReason) {
        this.addReason = addReason;
    }

    public String getAddReasonDesc() {
        return addReasonDesc;
    }

    public void setAddReasonDesc(String addReasonDesc) {
        this.addReasonDesc = addReasonDesc;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
