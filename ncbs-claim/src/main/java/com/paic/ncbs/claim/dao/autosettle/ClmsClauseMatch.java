package com.paic.ncbs.claim.dao.autosettle;

import java.util.Date;

/**
 * 条款匹责配置表
 */
public class ClmsClauseMatch {

    /**
     * 主键
     */
    private String create;

    /**
     * 外键
     */
    private Integer configId;

    /**
     * 自动匹配顺序
     */
    private Integer matchSequece;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 险种代码
     */
    private String planCode;

    /**
     * 险种名称
     */
    private String planName;

    /**
     * 方案代码
     */
    private String riskGroupCode;

    /**
     * 方案名称
     */
    private String riskGroupName;

    /**
     * 责任代码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 责任明细代码
     */
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    private String dutyDetailName;

    /**
     * 发票类型
     */
    private String billType;

    /**
     * 票据明细包含的关键字
     */
    private String matchKeys;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getMatchSequece() {
        return matchSequece;
    }

    public void setMatchSequece(Integer matchSequece) {
        this.matchSequece = matchSequece;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getRiskGroupCode() {
        return riskGroupCode;
    }

    public void setRiskGroupCode(String riskGroupCode) {
        this.riskGroupCode = riskGroupCode;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    public String getDutyDetailCode() {
        return dutyDetailCode;
    }

    public void setDutyDetailCode(String dutyDetailCode) {
        this.dutyDetailCode = dutyDetailCode;
    }

    public String getDutyDetailName() {
        return dutyDetailName;
    }

    public void setDutyDetailName(String dutyDetailName) {
        this.dutyDetailName = dutyDetailName;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getMatchKeys() {
        return matchKeys;
    }

    public void setMatchKeys(String matchKeys) {
        this.matchKeys = matchKeys;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
