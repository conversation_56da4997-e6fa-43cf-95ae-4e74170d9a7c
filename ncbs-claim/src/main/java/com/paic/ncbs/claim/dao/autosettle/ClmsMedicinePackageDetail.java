package com.paic.ncbs.claim.dao.autosettle;

import java.util.Date;

/**
 * 药品模板药品表
 */
public class ClmsMedicinePackageDetail {

    /**
     * 主键
     */
    private String create;

    /**
     * 包外键id
     */
    private Integer packageId;

    /**
     * 药品名称
     */
    private String medicineName;

    /**
     * 药品分类
     */
    private String medicineCategory;

    /**
     * 适应症
     */
    private String indication;

    /**
     * 有效标志
     */
    private Boolean validFlag;

    /**
     * 备注描述
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public Integer getPackageId() {
        return packageId;
    }

    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    public String getMedicineName() {
        return medicineName;
    }

    public void setMedicineName(String medicineName) {
        this.medicineName = medicineName;
    }

    public String getMedicineCategory() {
        return medicineCategory;
    }

    public void setMedicineCategory(String medicineCategory) {
        this.medicineCategory = medicineCategory;
    }

    public String getIndication() {
        return indication;
    }

    public void setIndication(String indication) {
        this.indication = indication;
    }

    public Boolean getValidFlag() {
        return validFlag;
    }

    public void setValidFlag(Boolean validFlag) {
        this.validFlag = validFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
