package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatch;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 发票匹责结果表Mapper
 */
public interface ClmsInvoiceMatchMapper {

    /**
     * 根据主键查询
     */
    ClmsInvoiceMatch selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsInvoiceMatch record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsInvoiceMatch record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsInvoiceMatch record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsInvoiceMatch record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsInvoiceMatch> selectAll();

}
