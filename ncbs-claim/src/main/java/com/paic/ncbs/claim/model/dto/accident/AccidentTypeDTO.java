package com.paic.ncbs.claim.model.dto.accident;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("事故类型")
@Getter
@Setter
public class AccidentTypeDTO extends EntityDTO {

    private static final long serialVersionUID = 5904014834880853765L;

    private String idCppAccidentType;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("保单号")
    private String policyNo;
    @ApiModelProperty("事故类型")
    private String accidentType;
    @ApiModelProperty("事故类型详情")
    private String accidentTypeDetail;
    @ApiModelProperty("数据来源")
    private String migrateFrom;

}