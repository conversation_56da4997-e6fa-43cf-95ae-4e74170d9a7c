package com.paic.ncbs.claim.dao.autosettle;

import java.util.Date;

/**
 * 条款匹责疾病范围表
 */
public class ClmsClauseMatchDisease {

    /**
     * 主键
     */
    private String create;

    /**
     * 外键
     */
    private Integer configId;

    /**
     * 自动匹责id
     */
    private Integer matchId;

    /**
     * 一级医疗目录id
     */
    private Integer medicalDir1Id;

    /**
     * 一级医疗目录名称
     */
    private String medicalDir1Name;

    /**
     * 二级医疗目录id
     */
    private Integer medicalDir2Id;

    /**
     * 二级医疗目录名称
     */
    private String medicalDir2Name;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getMatchId() {
        return matchId;
    }

    public void setMatchId(Integer matchId) {
        this.matchId = matchId;
    }

    public Integer getMedicalDir1Id() {
        return medicalDir1Id;
    }

    public void setMedicalDir1Id(Integer medicalDir1Id) {
        this.medicalDir1Id = medicalDir1Id;
    }

    public String getMedicalDir1Name() {
        return medicalDir1Name;
    }

    public void setMedicalDir1Name(String medicalDir1Name) {
        this.medicalDir1Name = medicalDir1Name;
    }

    public Integer getMedicalDir2Id() {
        return medicalDir2Id;
    }

    public void setMedicalDir2Id(Integer medicalDir2Id) {
        this.medicalDir2Id = medicalDir2Id;
    }

    public String getMedicalDir2Name() {
        return medicalDir2Name;
    }

    public void setMedicalDir2Name(String medicalDir2Name) {
        this.medicalDir2Name = medicalDir2Name;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
