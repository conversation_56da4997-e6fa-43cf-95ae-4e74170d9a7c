package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfig;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理赔配置表Mapper
 */
public interface ClmsClauseConfigMapper {

    /**
     * 根据主键查询
     */
    ClmsClauseConfig selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsClauseConfig record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsClauseConfig record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsClauseConfig record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsClauseConfig record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsClauseConfig> selectAll();

}
