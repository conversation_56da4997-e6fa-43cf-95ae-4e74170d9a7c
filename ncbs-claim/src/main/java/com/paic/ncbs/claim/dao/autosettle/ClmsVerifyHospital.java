package com.paic.ncbs.claim.dao.autosettle;

import java.util.Date;

/**
 * 条款核责配置-医院范围表
 */
public class ClmsVerifyHospital {

    /**
     * 主键
     */
    private String create;

    /**
     * 外键
     */
    private Integer configId;

    /**
     * 核责等级
     */
    private Integer verifyLevel;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 险种代码
     */
    private String planCode;

    /**
     * 险种名称
     */
    private String planName;

    /**
     * 方案代码
     */
    private String riskGroupCode;

    /**
     * 方案名称
     */
    private String riskGroupName;

    /**
     * 责任代码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 责任明细代码
     */
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    private String dutyDetailName;

    /**
     * 医院等级
     */
    private String hospitalLevel;

    /**
     * 医院级别
     */
    private String hospitalGrade;

    /**
     * 医院性质
     */
    private String hospitalNature;

    /**
     * 就诊类型
     */
    private String medicalConsultation;

    /**
     * 医院区域类型
     */
    private String hospitalRegion;

    /**
     * 除外省市
     */
    private String exceptRegion;

    /**
     * 协议扩展-医院包ID
     */
    private Integer includePackageId;

    /**
     * 除外-医院包ID
     */
    private Integer excludePackageId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getVerifyLevel() {
        return verifyLevel;
    }

    public void setVerifyLevel(Integer verifyLevel) {
        this.verifyLevel = verifyLevel;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getRiskGroupCode() {
        return riskGroupCode;
    }

    public void setRiskGroupCode(String riskGroupCode) {
        this.riskGroupCode = riskGroupCode;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    public String getDutyDetailCode() {
        return dutyDetailCode;
    }

    public void setDutyDetailCode(String dutyDetailCode) {
        this.dutyDetailCode = dutyDetailCode;
    }

    public String getDutyDetailName() {
        return dutyDetailName;
    }

    public void setDutyDetailName(String dutyDetailName) {
        this.dutyDetailName = dutyDetailName;
    }

    public String getHospitalLevel() {
        return hospitalLevel;
    }

    public void setHospitalLevel(String hospitalLevel) {
        this.hospitalLevel = hospitalLevel;
    }

    public String getHospitalGrade() {
        return hospitalGrade;
    }

    public void setHospitalGrade(String hospitalGrade) {
        this.hospitalGrade = hospitalGrade;
    }

    public String getHospitalNature() {
        return hospitalNature;
    }

    public void setHospitalNature(String hospitalNature) {
        this.hospitalNature = hospitalNature;
    }

    public String getMedicalConsultation() {
        return medicalConsultation;
    }

    public void setMedicalConsultation(String medicalConsultation) {
        this.medicalConsultation = medicalConsultation;
    }

    public String getHospitalRegion() {
        return hospitalRegion;
    }

    public void setHospitalRegion(String hospitalRegion) {
        this.hospitalRegion = hospitalRegion;
    }

    public String getExceptRegion() {
        return exceptRegion;
    }

    public void setExceptRegion(String exceptRegion) {
        this.exceptRegion = exceptRegion;
    }

    public Integer getIncludePackageId() {
        return includePackageId;
    }

    public void setIncludePackageId(Integer includePackageId) {
        this.includePackageId = includePackageId;
    }

    public Integer getExcludePackageId() {
        return excludePackageId;
    }

    public void setExcludePackageId(Integer excludePackageId) {
        this.excludePackageId = excludePackageId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
