package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyLog;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 自动核责日志主表Mapper
 */
public interface ClmsVerifyLogMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyLog selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyLog record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyLog record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyLog record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyLog record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyLog> selectAll();

}
