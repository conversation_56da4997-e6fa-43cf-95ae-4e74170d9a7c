package com.paic.ncbs.claim.model.dto.duty;


import com.paic.ncbs.claim.model.dto.other.EntityDTO;

/**
 * @description: 机场信息DTO
 */
public class AirportInfoDTO extends EntityDTO {
	private static final long serialVersionUID = 8487124743497891294L;
	private String idAhcsAirportInfo;// varchar2(32) n 主键id
	private String airportCode;// varchar2(5) n 机场三字码
	private String airportName;// varchar2(100) n 机场名称
	private String cityCode;// varchar2(20) y 城市代码
	private String cityName;// varchar2(100) y 城市名称
	private String provinceCode;// varchar2(20) y 省会代码
	private String provinceName;// varchar2(20) y 省会名称
	private String airportType;// varchar2(1) n 机场类型(0-国内机场,1国际机场)

	public String getIdAhcsAirportInfo() {
		return idAhcsAirportInfo;
	}

	public void setIdAhcsAirportInfo(String idAhcsAirportInfo) {
		this.idAhcsAirportInfo = idAhcsAirportInfo;
	}

	public String getAirportCode() {
		return airportCode;
	}

	public void setAirportCode(String airportCode) {
		if (null != airportCode) {
			this.airportCode = airportCode.toUpperCase();
		} else {
			this.airportCode = airportCode;
		}
	}

	public String getAirportName() {
		return airportName;
	}

	public void setAirportName(String airportName) {
		this.airportName = airportName;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getAirportType() {
		return airportType;
	}

	public void setAirportType(String airportType) {
		this.airportType = airportType;
	}

}
