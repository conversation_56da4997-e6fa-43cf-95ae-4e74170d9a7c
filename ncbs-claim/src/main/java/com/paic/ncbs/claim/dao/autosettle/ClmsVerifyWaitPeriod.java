package com.paic.ncbs.claim.dao.autosettle;

import java.util.Date;

/**
 * 条款核责配置-等待期表
 */
public class ClmsVerifyWaitPeriod {

    /**
     * 主键
     */
    private String create;

    /**
     * 外键
     */
    private Integer configId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 险种代码
     */
    private String planCode;

    /**
     * 险种名称
     */
    private String planName;

    /**
     * 方案代码
     */
    private String riskGroupCode;

    /**
     * 方案名称
     */
    private String riskGroupName;

    /**
     * 新保疾病等待期（天）
     */
    private Integer diseaseWaitPeriod;

    /**
     * 新保意外等待期（天）
     */
    private Integer accidentWaitPeriod;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getRiskGroupCode() {
        return riskGroupCode;
    }

    public void setRiskGroupCode(String riskGroupCode) {
        this.riskGroupCode = riskGroupCode;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public Integer getDiseaseWaitPeriod() {
        return diseaseWaitPeriod;
    }

    public void setDiseaseWaitPeriod(Integer diseaseWaitPeriod) {
        this.diseaseWaitPeriod = diseaseWaitPeriod;
    }

    public Integer getAccidentWaitPeriod() {
        return accidentWaitPeriod;
    }

    public void setAccidentWaitPeriod(Integer accidentWaitPeriod) {
        this.accidentWaitPeriod = accidentWaitPeriod;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
