package com.paic.ncbs.claim.dao.autosettle;

import java.util.Date;

/**
 * 发票匹责结果表
 */
public class ClmsInvoiceMatch {

    /**
     * 主键
     */
    private String create;

    /**
     * 案件号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 匹责类型
     */
    private String matchType;

    /**
     * 自动匹责的配置项id-自动匹责时填充
     */
    private Integer configId;

    /**
     * 发票账单信息表主键
     */
    private String idAhcsInvoiceInfo;

    /**
     * 执行成功标志
     */
    private Integer matchSign;

    /**
     * 失败描述
     */
    private String failDesc;

    /**
     * 匹责结论
     */
    private String matchResult;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    private String updatedBy;

    /**
     * 最新修改时间
     */
    private Date sysUtime;

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public String getIdAhcsInvoiceInfo() {
        return idAhcsInvoiceInfo;
    }

    public void setIdAhcsInvoiceInfo(String idAhcsInvoiceInfo) {
        this.idAhcsInvoiceInfo = idAhcsInvoiceInfo;
    }

    public Integer getMatchSign() {
        return matchSign;
    }

    public void setMatchSign(Integer matchSign) {
        this.matchSign = matchSign;
    }

    public String getFailDesc() {
        return failDesc;
    }

    public void setFailDesc(String failDesc) {
        this.failDesc = failDesc;
    }

    public String getMatchResult() {
        return matchResult;
    }

    public void setMatchResult(String matchResult) {
        this.matchResult = matchResult;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getSysCtime() {
        return sysCtime;
    }

    public void setSysCtime(Date sysCtime) {
        this.sysCtime = sysCtime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getSysUtime() {
        return sysUtime;
    }

    public void setSysUtime(Date sysUtime) {
        this.sysUtime = sysUtime;
    }

}
