package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifySublog;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 自动核责日志子表Mapper
 */
public interface ClmsVerifySublogMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifySublog selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifySublog record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifySublog record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifySublog record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifySublog record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifySublog> selectAll();

}
