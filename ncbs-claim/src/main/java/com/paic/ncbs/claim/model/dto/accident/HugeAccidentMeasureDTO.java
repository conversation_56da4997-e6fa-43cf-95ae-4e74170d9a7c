package com.paic.ncbs.claim.model.dto.accident;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel("举措")
public class HugeAccidentMeasureDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    private String idAhcsHugeAccidentMeasure;

    private String idAhcsHugeAccidentInfo;
    @ApiModelProperty("举措类型(取自数据常量：AHCS_HUGE_MEASURE)")
    private String measureType;
    @ApiModelProperty("重大灾难服务举措(多个code,以英文逗号分隔,取自数据常量：AHCS_HUGE_MEASURE)")
    private String measureCode;


}